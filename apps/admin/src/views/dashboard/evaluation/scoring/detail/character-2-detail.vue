<template>
    <b-layout type="content">
        <b-layout direction="vertical">
            <div class="form-section">
                <b-section type="title" :bgColor="true">
                    <b-title type="preline" size="small"> 五维性格测评 2.0 </b-title>
                </b-section>
                <TabLayout
                    v-model="currentTab"
                    v-loading="detailLoading"
                    :showFooter="currentTab === 1"
                    :loading="loading"
                    :tabList="[
                        { label: '参数设置', value: 1 },
                        { label: '计分规则导入', value: 2 },
                        { label: '企业定制模型', value: 3 },
                    ]"
                    @cancel="cancel"
                    @save="save"
                >
                    <!-- 参数设置 -->
                    <div v-if="currentTab === 1" class="form-section">
                        <!-- 基础参数 -->
                        <div class="sub-title">
                            <h4>基础参数</h4>
                        </div>
                        <b-form ref="formRef" scrollToFirstError :model="queryData" useGrid :gridProps="{ gap: 10, columns: 2, rows: 3 }" layout="inline" @submit.prevent>
                            <b-form-item label="参数a" field="paramA" asteriskPosition="end" :rules="[{ required: true, type: 'number', message: '请填写参数a' }]">
                                <b-input-number v-model.trim="queryData.paramA" placeholder="请填写1-99内整数" hideButton :min="1" :max="99" :precision="0" />
                            </b-form-item>
                            <b-form-item label="参数b" field="paramB" asteriskPosition="end" :rules="[{ required: true, type: 'number', message: '请填写参数b' }]">
                                <b-input-number v-model.trim="queryData.paramB" placeholder="请填写1-99内整数" hideButton :min="1" :max="99" :precision="0" />
                            </b-form-item>
                            <b-form-item
                                label="得分下限"
                                field="scoreMin"
                                asteriskPosition="end"
                                :rules="[
                                    { required: true, type: 'number', message: '请填写得分下限' },
                                    { validator: (value, callback) => formValidator(value, callback, 'scoreMin') },
                                ]"
                            >
                                <b-input-number
                                    v-model.trim="queryData.scoreMin"
                                    placeholder="请填写1-99内整数"
                                    hideButton
                                    :min="1"
                                    :max="99"
                                    :precision="0"
                                    @change="handleChange('base')"
                                />
                            </b-form-item>
                            <b-form-item
                                label="得分上限"
                                field="scoreMax"
                                asteriskPosition="end"
                                :rules="[
                                    { required: true, type: 'number', message: '请填写得分上限' },
                                    { validator: (value, callback) => formValidator(value, callback, 'scoreMax') },
                                ]"
                            >
                                <b-input-number
                                    v-model.trim="queryData.scoreMax"
                                    placeholder="请填写1-99内整数"
                                    hideButton
                                    :min="1"
                                    :max="99"
                                    :precision="0"
                                    @change="handleChange('base')"
                                />
                            </b-form-item>
                        </b-form>

                        <!-- 等级划分 -->
                        <div class="sub-title">
                            <h4>等级划分</h4>
                        </div>
                        <b-form
                            ref="levelFormRef"
                            scrollToFirstError
                            class="level-form"
                            :model="{ levels: formLevelData }"
                            useGrid
                            :gridProps="{ gap: [20, 10], columns: 5 }"
                            layout="inline"
                            @submit.prevent
                        >
                            <template v-for="(item, index) in LEVEL_LABELS" :key="index">
                                <b-form-item
                                    :field="`levels.${index}`"
                                    :label="item.label"
                                    asteriskPosition="end"
                                    :rules="!item.hide ? [{ validator: (value, callback) => formLevelValidator(value, callback, index) }] : []"
                                >
                                    <template v-if="!item.hide">
                                        <b-input-number
                                            v-model.trim="formLevelData[index]"
                                            @change="levelFormRef.validate()"
                                            class="dimension-level"
                                            placeholder="请输入"
                                            hideButton
                                            :min="0.1"
                                            :max="99.9"
                                            :precision="1"
                                        />
                                    </template>
                                </b-form-item>
                            </template>
                        </b-form>

                        <!-- 关键潜在素质分数计算 -->
                        <KeyPotentialQualityConfig ref="keyPotentialRef" v-model="keyPotentialQualityData" />

                        <!-- 岗位素质模型匹配度分数计算 -->
                        <JobQualityModelConfig ref="jobQualityRef" v-model="jobQualityModelData" />

                        <!-- 团队角色分数计算 -->
                        <TeamRoleConfig ref="teamRoleRef" v-model="teamRoleData" />
                    </div>

                    <!-- 计分规则导入 -->
                    <ScoreRuleConfig v-else-if="currentTab === 2" :tableData="exportTableData" />

                    <!-- 企业定制模型 -->
                    <EnterpriseCustomModelConfig v-else />
                </TabLayout>
            </div>
        </b-layout>
    </b-layout>
</template>

<script setup lang="ts">
import type { CHARACTER_SCORE_QUERAY_DATA, Character2DetailData, QualityConfigItem } from '@/views/dashboard/evaluation/scoring/type';
import EnterpriseCustomModelConfig from '../components/enterprise-custom-model-config.vue';
import { pushRouter } from '@/router-v2';
import { AdminModuleCode } from '@/router-v2/types';
import { _scoreParamDetail, _scoreParamSave } from '@/services/api/scoring';
import { onMounted, onUnmounted, ref, watch, reactive } from 'vue';
import { onBeforeRouteLeave, useRoute } from 'vue-router';

// 全局变量声明
declare const Toast: {
    success: (message: string) => void;
    danger: (message: string) => void;
};

declare const Dialog: {
    open: (options: { type: string; title: string; content: string; confirm: () => void; cancel?: () => void }) => void;
};

import ScoreRuleConfig from '../components/score-rule-config.vue';
import TabLayout from '../components/tab-layout.vue';
import KeyPotentialQualityConfig from '../components/key-potential-quality-config.vue';
import JobQualityModelConfig from '../components/job-quality-model-config.vue';
import TeamRoleConfig from '../components/team-role-config.vue';
// import EnterpriseCustomModelConfig from '../components/enterprise-custom-model-config.vue';

defineOptions({
    name: 'Character2Detail',
});

const $route = useRoute();
const currentTab = ref(1);
const formRef = ref();
const levelFormRef = ref();
const loading = ref(false);

// 子组件引用
const keyPotentialRef = ref();
const jobQualityRef = ref();
const teamRoleRef = ref();

// 等级标签配置
const LEVEL_LABELS = [
    { label: '低 <', hide: false },
    { label: '≤ 较低 <', hide: false },
    { label: '≤ 中 <', hide: false },
    { label: '≤ 较高 <', hide: false },
    { label: '≤ 高', hide: true },
];

// 基础参数数据
const queryData = ref<CHARACTER_SCORE_QUERAY_DATA>({
    paramA: undefined,
    paramB: undefined,
    scoreMin: undefined,
    scoreMax: undefined,
});

// 等级划分数据
const formLevelData = ref<(number | undefined)[]>([]);

// 关键潜在素质数据
const keyPotentialQualityData = ref<QualityConfigItem[]>([]);

// 岗位素质模型数据
const jobQualityModelData = ref<QualityConfigItem[]>([]);

// 团队角色数据
const teamRoleData = ref<QualityConfigItem[]>([]);

// 导出表格数据
const exportTableData = ref<any[]>([]);

// 表单验证
function formValidator(value: number, callback: (message?: string) => void, filed: string) {
    if (value || value === 0) {
        if (filed === 'scoreMax' && queryData.value.scoreMin !== undefined && value <= queryData.value.scoreMin) {
            callback('得分上限必须大于得分下限');
        }
        return;
    }
    callback();
}

function formLevelValidator(value: number, callback: (message?: string) => void, index: number) {
    if (!(value || value === 0)) {
        return callback('请填写等级划分');
    }
    if (index < LEVEL_LABELS.length - 1 && formLevelData.value[index + 1] !== undefined && value >= (formLevelData.value?.[index + 1] as number)) {
        return callback('数值必须小于右侧值');
    }
    return callback();
}

function handleChange(key: string) {
    if (key === 'base') {
        formRef.value.validateField(['scoreMax']);
    }
}

// 保存和取消
const saved = ref(false);
async function save() {
    // 校验基础表单
    const formRes = await formRef.value.validate();
    const levelRes = await levelFormRef.value.validate();

    // 校验子组件表单
    let keyPotentialRes = true;
    let jobQualityRes = true;
    let teamRoleRes = true;

    try {
        if (keyPotentialRef.value?.validate) {
            keyPotentialRes = await keyPotentialRef.value.validate();
        }
        if (jobQualityRef.value?.validate) {
            jobQualityRes = await jobQualityRef.value.validate();
        }
        if (teamRoleRef.value?.validate) {
            teamRoleRes = await teamRoleRef.value.validate();
        }
    } catch (error) {
        console.error('子组件校验失败:', error);
        Toast.danger('表单校验失败，请检查填写内容');
        return;
    }

    // 如果任何表单校验失败，则不继续保存
    if (formRes || levelRes || !keyPotentialRes || !jobQualityRes || !teamRoleRes) {
        Toast.danger('请完善表单信息后再保存');
        return;
    }

    try {
        loading.value = true;
        const params = {
            productId: $route.query?.productId,
            paramA: queryData.value.paramA,
            paramB: queryData.value.paramB,
            paramC: {
                scoreMin: queryData.value.scoreMin,
                scoreMax: queryData.value.scoreMax,
                levelArray: formLevelData.value,
                keyPotentialQualityList: keyPotentialQualityData.value,
                positionQualityModelMatchList: jobQualityModelData.value,
                teamRoleList: teamRoleData.value,
            },
        };

        const { code } = await _scoreParamSave(params);
        if (code === 0) {
            saved.value = true;
            Toast.success('保存成功');
            cancel();
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        loading.value = false;
    }
}

function cancel() {
    pushRouter(AdminModuleCode.evaluationScoring.root);
}

// 数据初始化
const detailLoading = ref(false);
async function getScoringParamsDetail() {
    try {
        detailLoading.value = true;
        const { code, data } = await _scoreParamDetail({ productId: $route.query?.productId });
        if (code === 0) {
            initDataSource(data);
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        detailLoading.value = false;
    }
}

function initDataSource(data: Character2DetailData) {
    const { paramC, paramA, paramB } = data || {};

    queryData.value = {
        paramA,
        paramB,
        scoreMax: paramC?.scoreMax,
        scoreMin: paramC?.scoreMin,
    };

    // 确保传递给子组件的数据是响应式的，避免 toRefs 警告
    formLevelData.value = reactive(paramC?.levelArray || []);
    keyPotentialQualityData.value = reactive(paramC?.keyPotentialQualityList || []);
    jobQualityModelData.value = reactive(paramC?.positionQualityModelMatchList || []);
    teamRoleData.value = reactive(paramC?.teamRoleList || []);

    // 处理导出表格数据
    exportTableData.value = [];
    if (data?.fileParam) {
        // 五维性格测评2.0特定的文件类型
        const CHARACTER_2_FILE_KEYS = ['product21PercentLevelScoreMatch'];
        CHARACTER_2_FILE_KEYS.forEach((key) => {
            if (data.fileParam[key]) {
                exportTableData.value.push({
                    ...data.fileParam[key],
                    name: key === 'product21PercentLevelScoreMatch' ? '百分等级P分—分数对照表' : data.fileParam[key].name,
                    code: key,
                });
            }
        });
    }
}

getScoringParamsDetail();

// 数据变更监听
const dataChanged = ref(false);
function startWatchData() {
    watch(
        [() => queryData.value, () => formLevelData.value, () => keyPotentialQualityData.value, () => jobQualityModelData.value, () => teamRoleData.value],
        () => {
            // 这里应该与原始数据进行比较，简化处理
            dataChanged.value = true;
            if (dataChanged.value) {
                window.onbeforeunload = function () {
                    return '确认离开此页面吗？';
                };
            } else {
                window.onbeforeunload = null;
            }
        },
        { deep: true }
    );
}

onMounted(() => {
    startWatchData();
});

onUnmounted(() => {
    window.onbeforeunload = null;
});

onBeforeRouteLeave((to, _from, next) => {
    if (to.path === AdminModuleCode.evaluationScoring.root) {
        to.query.resetPage = '1';
    }
    if (saved.value) {
        next();
    } else {
        if (dataChanged.value) {
            Dialog.open({
                type: 'warning',
                title: '离开提示',
                content: '当前数据尚未保存，确定要离开吗？',
                confirm() {
                    next();
                },
                cancel() {
                    next(false);
                },
            });
        } else {
            next();
        }
    }
});
</script>

<style lang="less" scoped>
@import '@/styles/evaluation/scoring/character-detail.less';

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.level-form {
    margin-bottom: 20px;
}

.dimension-level {
    width: 100%;
}

.config-placeholder {
    padding: 40px;
    text-align: center;
    color: #86909c;
    font-size: 14px;
    background: #f7f8fa;
    border-radius: 4px;
    margin: 20px 0;
}
</style>
